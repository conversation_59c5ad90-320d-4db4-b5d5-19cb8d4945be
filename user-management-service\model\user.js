const mongoose = require('mongoose');

const userSchema = new mongoose.Schema({
    // Basic user fields
    name: {
        type: String,
        required: true,
        trim: true,
    },
    email: {
        type: String,
        required: true,
        unique: true,
        trim: true,
    },
    phone: {
        type: String,
        required: true,
        unique: true,
        trim: true,
    },
    password: {
        type: String,
        required: true,
        minlength: 6,
    },
    avatar: {
        type: String,
        default:
            'https://res.cloudinary.com/dyyfuewh4/image/upload/v1748331728/bronze-membership-icon-default-avatar-profile-icon-membership-icon-social-media-user-image-illustration-vector_ybxqsb.jpg',
    },
    role: {
        type: String,
        enum: ['admin', 'user', 'contractor', 'broker'],
        default: 'user',
    },
    location: {
        type: String,
    },
    documents: {
        type: [String],
        default: []
    },
    specialties: {
        type: [String],
        default: []
    },
    portfolio: {
        type: [String],
        default: []
    },
    experience: {
        type: Number,
        default: 0
    },
    verifiedByBroker: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
    },
    verificationStatus: {
        type: String,
        enum: ['pending', 'verified', 'rejected'],
        default: 'pending'
    },
    approvalDate: {
        type: Date
    },
    ratings: {
        type: [Number],
        default: []
    },
    isActive: {
        type: Boolean,
        default: true
    },
    isEmailVerified: {
        type: Boolean,
        default: false
    },
    isPhoneVerified: {
        type: Boolean,
        default: false
    },
    lastLoginAt: {
        type: Date
    },
}, {
    timestamps: true
});

const User = mongoose.model('User', userSchema);
module.exports = User;
